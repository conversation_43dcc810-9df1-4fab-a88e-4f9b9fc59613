{"expo": {"name": "emergent-mobile", "slug": "emergent-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo_build.png", "scheme": "emergentmobile", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.kacppian.emergentmobile", "entitlements": {"aps-environment": "development"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo_build.png", "backgroundColor": "#000"}, "edgeToEdgeEnabled": true, "package": "com.kacppian.emergentmobile"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logo_build.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#000"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "a21a89c4-50d4-456e-8650-e39211874d11"}}}}