import { useState, useRef, useEffect, useCallback, useMemo, memo } from "react";
import { Loader2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { agentApi } from "@/services/agentApi";
import { useToast } from "@/hooks/use-toast";
import AttachIcon from "@/assets/attach.svg";
import PauseIcon from "@/assets/pause.svg";
import SendIcon from "@/assets/send.svg";
import { GitHubPushModal } from "./modals/GitHubPushModal"; // Import the GitHubPushModal component
import { useImageAttachments } from "@/hooks/useImageAttachments";
import { ImageData } from "@/types/message";
import AttachmentCross from "@/assets/attachment-cross.svg";
import { useTabState } from "./TabBar";
import WhiteGithubIcon from "@/assets/white-github.svg";
import { useGitHub } from "@/hooks/useGitHubAPI";
import { TokenDisplay } from "./TokenDisplay";
import { cn } from "@/lib/utils";
import { useAuth, useCredits } from "@/contexts";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import {
  getSubagentName,
  getSubagentGradient,
  getDynamicStatusColor,
  getSubagentStyle,
} from "@/utils/commonUtils";
import { calculateAgentStatus, type AgentStatus } from "@/utils/agentStatusUtils";
// @ts-ignore
import animatedSpinner from "@/assets/animated-spinner.gif";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipArrow,
} from "@/components/ui/tooltip";

//Assets

import GithubDot from "@/assets/github/git-dot.svg"  
import ForkSVG from "@/assets/fork/fork.svg";
import { URL_LINKS } from "@/constants/constants";
import { isMobileDevice } from "@/lib/utils/deviceDetection";
import { hasSeenForkIntro, markForkIntroAsSeen } from "@/lib/utils/modalStateManager";

// Memoized status components
const StatusIndicator = memo(
  ({ status, colors }: { status: string; colors: any }) => {
    // Use the color directly from the colors object instead of parsing from class
    const dotColor = colors[status].color || "#5FD3F3";

    return (
      <div
        className={cn(
          `w-4 h-4 rounded-lg flex justify-center items-center`,
          status != "stopped" && status != "exit_cost" && "animate-status-pulse"
        )}
        style={
          {
            backgroundColor: `${dotColor}20`, // 20% opacity for background
            "--status-pulse-color": `${dotColor}70`, // 70 is hex for ~44% opacity
            "--status-pulse-transparent": `${dotColor}00`, // 00 is hex for 0% opacity
          } as React.CSSProperties
        }
      >
        <div
          className="w-2 h-2 rounded"
          style={{ backgroundColor: dotColor }}
        />
      </div>
    );
  }
);

const StatusText = memo(
  ({
    status,
    colors,
    statusText,
  }: {
    status: string;
    colors: any;
    statusText: any;
  }) => {
    return (
      <span
        className="text-[12px] md:text-[16px] font-medium md:text-nowrap"
        style={{ color: colors[status].color }}
      >
        {statusText[status]}
      </span>
    );
  }
);

interface ChatInputProps {
  onSubmit: (message: string, images: any) => void;
  placeholder?: string;
  isDisabled?: boolean;
  agentState?: {
    agent_running: boolean;
    job_running: boolean;
  } | null;
  containerId?: string | null;
  jobDetails?: {
    job_id: string;
    traj_path?: string;
    container_id?: string;
    createdBy?: string;
  };
  lastGithubUsed?: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  } | null;
  acc_cost?: number;
  max_budget?: number;
  onAddToken: () => void;
  onDockerCommit?: () => void;
  isCloudFlow: boolean;
  clientRefId?: string;
  modelName?: string;
  agentName?: string;
  isSubagentActive?: boolean;
  hideStatus?: boolean;
  hideTokens?: boolean;
  disableWhenSubagentActive?: boolean;
  onPause?: ({ origin }: { origin?: "MainInput" | "SubagentButton" }) => void;
  onOpenSubagentPanel?: () => void;
  showFinishSubagentText?: boolean;
  pauseWasClicked?: boolean;
  showImages?: boolean;
  inputValue?: string;
  onImagesChange?: (images: ImageData[]) => void;
  onGitHubPush?: (repoDetails: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  }) => void;
  isEmergentUser?: boolean;
  handleDeploy?: () => void;
  subagentName?: string;
  fromSubagentPanel?: boolean;
  isPauseLoading?: boolean;
  buildMode: "brainstorming_requested" | "brainstorming_done" | "build" | null;
  forked_status?: "running" | "success" | "failed" | null;
  modalOpen?: {
    fork: boolean;
  };
  setModalOpen?: (open: { fork: boolean }) => void;
}

export const ChatInput = memo(
  ({
    onSubmit,
    placeholder = "Message Neo",
    isDisabled,
    agentState,
    containerId,
    jobDetails,
    acc_cost,
    max_budget,
    onAddToken,
    isCloudFlow,
    clientRefId,
    inputValue,
    modelName,
    agentName,
    isSubagentActive,
    hideStatus = false,
    hideTokens = false,
    disableWhenSubagentActive = false,
    onPause,
    onOpenSubagentPanel,
    showFinishSubagentText,
    pauseWasClicked,
    onImagesChange,
    lastGithubUsed,
    showImages = true,
    subagentName,
    onGitHubPush,
    modalOpen,
    setModalOpen,
    fromSubagentPanel = false,
    isPauseLoading: externalIsPauseLoading,
    buildMode,
    forked_status,
  }: ChatInputProps) => {
    const { toast } = useToast();
    const [localValue, setLocalValue] = useState("");
    const [isGitHubPushModalOpen, setIsGitHubPushModalOpen] = useState(false);
    const [sendAsHimRequest, setSendAsHimRequest] = useState(false);
    const [showForkTooltip, setShowForkTooltip] = useState(false);
    const [hasSeenForkIntroLocal, setHasSeenForkIntroLocal] = useState(hasSeenForkIntro());
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [agentStatus, setAgentStatus] = useState<AgentStatus>("stopped");

    // Use external pause loading state if provided, otherwise use local state
    const isPauseLoading = externalIsPauseLoading ?? false;
    const { isConnected: isGitHubConnected } = useGitHub();

    const { user } = useAuth();

    const { credits } = useCredits();
    const isEmergentUser = useIsEmergentUser();
    const { getPendingMessage, getActiveTab, removePendingMessage } =
      useTabState();

    // Track drag state for drag and drop functionality
    const [isDraggingOver, setIsDraggingOver] = useState(false);

    // Check if user has seen the fork introduction tooltip
    useEffect(() => {
      const hasSeenIntro = hasSeenForkIntro();
      setHasSeenForkIntroLocal(hasSeenIntro);

      const isForkButtonDisabled =
        (agentStatus !== "waiting" && agentStatus !== "subagent_waiting") ||
        buildMode !== "build" ||
        isSubagentActive;

      const mobileDevice = isMobileDevice();

      if (!hasSeenIntro && !isForkButtonDisabled && !mobileDevice) {
        const timer = setTimeout(() => {
          setShowForkTooltip(true);
        }, 1000); // 1 second delay

        return () => clearTimeout(timer);
      }
    }, [ agentStatus, buildMode, isSubagentActive]);

    // Handle dismissing the fork tooltip
    const handleDismissForkTooltip = useCallback(() => {
      setShowForkTooltip(false);
      setHasSeenForkIntroLocal(true);
      markForkIntroAsSeen();
    }, []);

    // Initialize image attachments
    const imageAttachments = useImageAttachments({
      maxImages: 5,
      maxSizeInMB: 5,
      maxPixelDimensions: 8000,
      maxPixelDimensionsMultiple: 2000,
      onImagesChange,
    });

    // Handle file input change for mobile compatibility
    const handleFileInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files && files.length > 0) {
          imageAttachments.handleImageSelect(files);
        }
        // Reset the input value to allow selecting the same file again
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      },
      [imageAttachments]
    );

    // Handle attachment button click with mobile compatibility
    const handleAttachmentClick = useCallback(() => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      } else {
        // Fallback to the original method if ref is not available
        imageAttachments.openFilePicker();
      }
    }, [imageAttachments]);

    useEffect(() => {
      if (inputValue) {
        setLocalValue(inputValue);
      }
    }, [inputValue]);

    const handlePauseAgent = useCallback(async () => {
      if (!jobDetails?.job_id || !agentState?.agent_running) {
        return;
      }

      // Call the parent's pause handler - status will be updated via useEffect
      if (onPause) {
        onPause({ origin: fromSubagentPanel ? "SubagentButton" : "MainInput" });
      }
    }, [jobDetails?.job_id, agentState?.agent_running, onPause]);

    const handleResumeAgent = useCallback(async () => {
      if (!jobDetails?.job_id || !localValue.trim()) return;

      // Check if admin user wants to send as HIM request or if it's automatically a HIM request
      const shouldSendAsHim = isEmergentUser && sendAsHimRequest && user?.id !== jobDetails.createdBy;

      if (shouldSendAsHim) {
        // console.log("HIM Message : xoxo", jobDetails.job_id, localValue.trim(), user?.id, jobDetails.createdBy);
        await agentApi.handleHimMessage(jobDetails.job_id, localValue.trim());
        setLocalValue("");
        return;
      }

      const images =
        imageAttachments.images.length > 0
          ? imageAttachments.images.map((img) => ({
              mime_type: img.mime_type,
              img_base64: img.img_base64,
            }))
          : [];

      const content = localValue.trim();
      const imagesContent = images;
      // Immediately send the message to the chat
      onSubmit(content, images);
      setLocalValue(""); // Clear input after sending

      try {
        // For cloud flow, use submitCloudJob with resume true
        const payload = {
          processor_type: "env_only",
          is_cloud: true,
          task: content,
          prompt_name: "",
          prompt_version: "latest",
          work_space_dir: "",
          model_name: modelName || "claude-3-5-sonnet-20241022",
          env_image: "",
          base64_image_list: imagesContent,
          human_timestamp: new Date().toISOString(),
        };

        await agentApi.submitCloudJob(payload, jobDetails?.job_id, true);
        setAgentStatus("running");

        // Clear images after sending
        if (imageAttachments.images.length > 0) {
          imageAttachments.clearImages();
        }
      } catch (error) {
        console.error("Failed to resume agent:", error);
        removePendingMessage(getActiveTab().id, content);
        setLocalValue(content);
        toast({
          title: "Error",
          description: "Failed to resume agent",
          variant: "destructive",
        });
      }
    }, [
      jobDetails?.job_id,
      containerId,
      localValue,
      isCloudFlow,
      toast,
      modelName,
      imageAttachments,
      isEmergentUser,
      sendAsHimRequest,
      user?.id,
    ]);

    // This function is no longer needed as we're handling GitHub push directly in the button click handler

    // Update agent status based on external state
    useEffect(() => {
      const newStatus = calculateAgentStatus({
        agentState,
        isSubagentActive,
        currentAgentStatus: agentStatus,
        isPauseLoading,
        forked_status,
      });

      if (newStatus !== agentStatus) {
        setAgentStatus(newStatus);
      }
    }, [agentState, isSubagentActive, agentStatus, isPauseLoading, forked_status]);

    // Memoize UI elements with dynamic colors
    const colors = useMemo(() => {
      const getDynamicColor = (status: string) => {
        const dynamicColor = getDynamicStatusColor(
          status,
          subagentName,
          isSubagentActive
        );
        return {
          color: dynamicColor,
        };
      };

      const colorValues = {
        running: getDynamicColor("running"),
        waiting: getDynamicColor("waiting"),
        exit_cost: getDynamicColor("exit_cost"),
        stopped: getDynamicColor("stopped"),
        stopping: getDynamicColor("stopping"),
        subagent_running: getDynamicColor("subagent_running"),
        subagent_waiting: getDynamicColor("subagent_waiting"),
        subagent_stopping: getDynamicColor("subagent_stopping"),
        forking: getDynamicColor("forking"),
      };

      return colorValues;
    }, [subagentName, isSubagentActive]);

    const statusText = useMemo(() => {
      const getAgentDisplayName = () => agentName || "Agent";
      const getSubagentDisplayName = () =>
        subagentName ? getSubagentName(subagentName) : "Subagent";

      return {
        exit_cost: `${getAgentDisplayName()} is offline`,
        running: `${getAgentDisplayName()} is running...`,
        waiting: `${getAgentDisplayName()} is awaiting human response...`,
        stopped: `${getAgentDisplayName()} is offline`,
        stopping: `${getAgentDisplayName()} is stopping gracefully. Usually takes a min`,
        subagent_running: `${getSubagentDisplayName()} is running...`,
        subagent_waiting: `${getSubagentDisplayName()} is awaiting human response...`,
        subagent_stopping: `${getSubagentDisplayName()} is gracefully stopping...`,
        forking: "Forking is in progress...",
      };
    }, [agentName, subagentName]);

    // Get dynamic background gradient based on agent status and subagent
    const getBackgroundGradient = () => {
      const defaultGradients = {
        running:
          "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(103, 203, 101, 0.2) 100%)",
        waiting:
          "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(95, 211, 243, 0.2) 100%)",
        stopped:
          "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(123, 123, 128, 0.2) 100%)",
        stopping:
          "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(244, 155, 87, 0.2) 100%)",
        exit_cost:
          "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(123, 123, 128, 0.2) 100%)",
      };

      if (fromSubagentPanel) {
        return (
          defaultGradients[agentStatus as keyof typeof defaultGradients] ||
          defaultGradients.stopped
        );
      }

      // If subagent is active and we have a subagent name, use subagent gradient
      if (isSubagentActive && subagentName) {
        const statusForGradient = agentStatus.startsWith("subagent_")
          ? (agentStatus.replace("subagent_", "") as
              | "running"
              | "waiting"
              | "stopping")
          : "waiting";
        return getSubagentGradient(subagentName, statusForGradient);
      }

      // For subagent statuses without specific subagent, use default purple gradient
      if (agentStatus.startsWith("subagent_")) {
        return "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(221, 153, 255, 1) 100%)";
      }

      const value =
        defaultGradients[agentStatus as keyof typeof defaultGradients] ||
        defaultGradients.stopped;

      if (
        agentStatus == "waiting" &&
        acc_cost !== undefined &&
        max_budget !== undefined &&
        acc_cost > max_budget
      ) {
        return "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%)";
      }

      return value;
    };

    const buttonIcon = useMemo(() => {
      // Show loader when pause is in progress
      if (isPauseLoading) {
        return <Loader2 className="w-6 h-6 text-gray-500 animate-spin" />;
      }

      switch (agentStatus) {
        case "running":
        case "subagent_running":
        case "stopping":
        case "subagent_stopping":
          return <img src={PauseIcon} className="w-6 h-6" alt="pause" />;
        default:
          return <img src={SendIcon} className="w-6 h-6" alt="send" />;
      }
    }, [agentStatus, isPauseLoading]);

    // Check if input should be disabled
    const isInputDisabled = useMemo(() => {
      if (isEmergentUser) return false;

      if (forked_status == "running") {
        return true;
      }

      if (buildMode == "brainstorming_done" && agentStatus === "running") {
        return true;
      }

      // Disable when subagent is active and disableWhenSubagentActive is true
      if (
        (isSubagentActive || agentStatus === "subagent_waiting") &&
        !fromSubagentPanel
      ) {
        return true;
      }

      if (getPendingMessage(getActiveTab().id)?.content) {
        return true;
      }

      // Check if budget limit is reached
      if (
        acc_cost !== undefined &&
        max_budget !== undefined &&
        acc_cost > max_budget
      ) {
        return true;
      }

      if (credits < 0) {
        return true;
      }

      // Otherwise, use the standard disabled logic
      return isDisabled;
    }, [
      isDisabled,
      disableWhenSubagentActive,
      isSubagentActive,
      getActiveTab,
      getPendingMessage,
      acc_cost,
      max_budget,
      isEmergentUser,
      buildMode,
      agentStatus,
    ]);

    const adjustHeight = useCallback(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.style.height = "auto";
        textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
      }
    }, []);

    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setLocalValue(e.target.value);
        // Call adjustHeight after value change
        requestAnimationFrame(adjustHeight);
      },
      [adjustHeight]
    );

    // Initial height adjustment when component mounts
    useEffect(() => {
      adjustHeight();
    }, [adjustHeight]);

    // Adjust height whenever localValue changes
    useEffect(() => {
      adjustHeight();
    }, [localValue, adjustHeight]);

    const handleSubmitForm = useCallback(
      (e: React.FormEvent) => {
        e.preventDefault();

        if (isInputDisabled) return;

        // Check if admin user wants to send as HIM request or if it's automatically a HIM request
        const shouldSendAsHim = isEmergentUser && sendAsHimRequest && user?.id !== jobDetails?.createdBy;
        
        if (shouldSendAsHim) {
          handleResumeAgent();
          return;
        }

        if (
          agentStatus === "running" ||
          agentStatus === "subagent_running" ||
          agentStatus === "stopping" ||
          agentStatus === "subagent_stopping"
        ) {
          handlePauseAgent();
          //console.log("paused");
        } else if (
          (agentStatus === "waiting" && !fromSubagentPanel) ||
          (agentStatus === "subagent_waiting" && fromSubagentPanel)
        ) {
          if (!localValue.trim()) return;
          handleResumeAgent();
          setLocalValue("");
        }

        if (imageAttachments.images.length > 0) {
          imageAttachments.clearImages();
        }
      },
      [
        localValue,
        isDisabled,
        imageAttachments,
        agentStatus,
        handlePauseAgent,
        handleResumeAgent,
        jobDetails,
        isInputDisabled,
        isEmergentUser,
        sendAsHimRequest,
        user?.id,
      ]
    );

    const disabledSubmit = useMemo(() => {
      let buttonState = false;
      if (
        isPauseLoading ||
        agentStatus === "subagent_waiting" ||
        (agentStatus === "running" && buildMode === "brainstorming_done") ||
        pauseWasClicked
      ) {
        buttonState = true;
      }

      if (agentStatus === "subagent_waiting" && fromSubagentPanel) {
        buttonState = false;
      }

      return buttonState;
    }, [
      isPauseLoading,
      agentStatus,
      showFinishSubagentText,
      pauseWasClicked,
      buildMode,
      fromSubagentPanel,
    ]);

    const getPlaceHolderText = useCallback(() => {
      if (
        acc_cost !== undefined &&
        max_budget !== undefined &&
        acc_cost > max_budget
      ) {
        return "Your budget limit has been reached. Please add more credits to resume your agent's task...";
      }
      if (forked_status == "running") {
        return "Please wait for forking to finish";
      }
      return placeholder;
    }, [placeholder, acc_cost, max_budget, forked_status]);

    return (
      <AnimatePresence>
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className={cn(
            "flex flex-col p-1 md:pt-1 pt-0 w-full rounded-[14px] backdrop-blur-[40px]"
          )}
          style={{ background: getBackgroundGradient() }}
        >
          <div className="flex items-center justify-between w-full p-2 md:px-3 md:py-2">
            <div className="flex items-center gap-2 max-w-[50%]">
              {/* Show selected images if any, otherwise show agent status */}
              {imageAttachments.images.length > 0 && (
                <div className="flex items-center gap-2">
                  <div className="flex flex-wrap gap-2">
                    {imageAttachments.isProcessing ? (
                      <div className="flex items-center justify-center py-1">
                        <Loader2 className="h-4 w-4 text-[#999] animate-spin mr-2" />
                        <span className="text-[#999] text-xs">
                          Processing...
                        </span>
                      </div>
                    ) : (
                      imageAttachments.images.map((image, index) => (
                        <div
                          key={index}
                          className="relative w-16 h-16 md:w-20 md:h-20 rounded-md border border-[#333] group"
                        >
                          <img
                            src={`data:${image.mime_type};base64,${image.img_base64}`}
                            alt={`Selected image ${index + 1}`}
                            className="w-full h-full object-cover z-[1] rounded-md"
                          />
                          <button
                            onClick={() => imageAttachments.removeImage(index)}
                            className="absolute top-[-6px] right-[-6px] bg-transparent z-[2] rounded-full p-1 transition-opacity"
                            aria-label="Remove image"
                            type="button"
                          >
                            <div className="bg-white rounded-full p-[2px]">
                              <img
                                src={AttachmentCross}
                                alt="Remove image"
                                className="w-2 h-2"
                              />
                            </div>
                          </button>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              )}
              {imageAttachments.images.length === 0 && !hideStatus && (
                <div className="flex items-center gap-2 md:gap-4">
                  <div className="flex items-center gap-2 min-w-[58vw] md:min-w-fit md:gap-4">
                    <StatusIndicator
                      status={
                        acc_cost !== undefined &&
                        max_budget !== undefined &&
                        acc_cost < max_budget
                          ? agentStatus
                          : "exit_cost"
                      }
                      colors={colors}
                    />
                    <StatusText
                      status={
                        acc_cost !== undefined &&
                        max_budget !== undefined &&
                        acc_cost < max_budget
                          ? agentStatus
                          : "exit_cost"
                      }
                      colors={colors}
                      statusText={statusText}
                    />
                  </div>

                  {agentStatus === "subagent_waiting" &&
                    onOpenSubagentPanel && (
                      <button
                        onClick={onOpenSubagentPanel}
                        className={`ml-2 px-2 py-1 text-xs font-semibold rounded-md transition-colors`}
                        style={{
                          backgroundColor: `${colors.subagent_waiting.color}`,
                          color: getSubagentStyle(subagentName || "").color,
                        }}
                        type="button"
                      >
                        Reply
                      </button>
                    )}
                </div>
              )}
            </div>
            {!hideTokens && imageAttachments.images.length === 0 && (
              <TokenDisplay
                acc_cost={acc_cost}
                max_budget={max_budget}
                onAddToken={onAddToken}
              />
            )}
          </div>
          <form
            onSubmit={handleSubmitForm}
            className={cn(
              "flex flex-1 flex-col bg-background border border-1 relative rounded-xl",
              isDraggingOver
                ? "border-[#5FD3F3]/50 border-2 border-dashed"
                : "border-[#252526]"
            )}
            onDragOver={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onDragEnter={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDraggingOver(true);
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Only set to false if we're leaving the form element (not entering a child)
              if (e.currentTarget.contains(e.relatedTarget as Node)) {
                return;
              }
              setIsDraggingOver(false);
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDraggingOver(false);

              // Process dropped files
              const files = e.dataTransfer.files;
              if (files.length > 0) {
                // Filter for image files only
                const imageFiles = Array.from(files).filter(
                  (file) =>
                    file.type.startsWith("image/") &&
                    ["image/jpeg", "image/png"].includes(file.type)
                );

                if (imageFiles.length > 0) {
                  imageAttachments.handleImageSelect(imageFiles);
                }
              }
            }}
          >
            {isDraggingOver && (
              <div className="absolute inset-0 z-10 flex items-center justify-center bg-background/80 rounded-xl">
                <div className="text-[#5FD3F3] text-center">
                  <p className="font-medium">Drop images here</p>
                  <p className="text-sm text-[#5FD3F3]/70">
                    JPEG, PNG (max 5MB)
                  </p>
                </div>
              </div>
            )}
            <textarea
              ref={textareaRef}
              value={localValue}
              onChange={handleChange}
              onPaste={(e) => {
                const items = e.clipboardData?.items;
                if (!items) return;

                const imageItems = Array.from(items).filter((item) =>
                  item.type.startsWith("image/")
                );
                if (imageItems.length === 0) return;

                e.preventDefault();

                const files = imageItems
                  .map((item) => item.getAsFile())
                  .filter(Boolean) as File[];
                if (files.length > 0) {
                  imageAttachments.handleImageSelect(files);
                }
              }}
              className={cn(
                `w-full resize-none bg-background outline-none text-[16px]  px-4 py-3 rounded-xl min-h-[64px] overflow-y-auto placeholder:text-white/50 ${
                  isInputDisabled ? "cursor-not-allowed opacity-50" : ""
                }`,
                fromSubagentPanel ? "max-h-[120px]" : "max-h-[200px]"
              )}
              style={{ lineHeight: "1.5" }}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmitForm(e);
                }
              }}
              placeholder={getPlaceHolderText()}
              rows={2}
              disabled={isInputDisabled}
            />
            <div className="flex items-center justify-between px-3 py-3 bg-background rounded-xl">
              <div
                className="relative flex items-center gap-2"
                ref={dropdownRef}
              >
                <button
                  type="button"
                  onClick={handleAttachmentClick}
                  className={cn(
                    "p-2 transition-colors duration-200 rounded-[30px] bg-[#FFFFFF14] hover:bg-gray-100/10 group/paperclip",
                    {
                      "opacity-50 cursor-not-allowed": isInputDisabled,
                    }
                  )}
                  disabled={isInputDisabled}
                >
                  <img
                    src={AttachIcon}
                    alt="Attach"
                    className="w-6 h-6 transition-transform duration-200 transform group-hover/paperclip:rotate-45"
                  />
                </button>

                {/* Hidden file input for mobile compatibility */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/jpeg,image/png"
                  multiple
                  onChange={handleFileInputChange}
                  style={{ display: "none" }}
                  aria-hidden="true"
                />

                {/* GitHub Push Button - Only visible when subagent is not active */}
                <button
                  disabled={
                    (agentStatus !== "waiting" &&
                      agentStatus !== "subagent_waiting") ||
                    buildMode != "build" ||
                    isSubagentActive
                  }
                  type="button"
                  onClick={() => {
                    // Directly open the GitHub push modal
                    setIsGitHubPushModalOpen(true);
                  }}
                  className={cn(
                    "px-3 pr-4 h-[40px] py-2 transition-colors duration-200 rounded-full flex items-center gap-2 bg-[#FFFFFF14] hover:bg-gray-100/10",
                    (agentStatus !== "waiting" &&
                      agentStatus !== "subagent_waiting") ||
                      buildMode != "build" ||
                      isSubagentActive
                      ? "opacity-50 cursor-not-allowed"
                      : "",
                    fromSubagentPanel ? "hidden" : ""
                  )}
                >
                  <img src={isGitHubConnected ? GithubDot : WhiteGithubIcon} alt="GitHub" className={cn("w-4 h-4", isGitHubConnected ? "scale-[1.5] mt-[2px]" : "")} />
                  <span className="max-md:hidden block text-sm font-medium text-[#E6E6E6]">
                    Save to GitHub
                  </span>
                  <span className="md:hidden visible text-sm font-medium text-[#E6E6E6]">
                    GitHub
                  </span>
                </button>

                {/* Admin HIM Button */}
                {isEmergentUser &&
                  jobDetails?.createdBy &&
                  user?.id !== jobDetails.createdBy && (
                    <button
                      type="button"
                      disabled={buildMode != "build"}
                      onClick={() => setSendAsHimRequest(!sendAsHimRequest)}
                      className={cn(
                        "px-3 h-[40px] py-2 transition-colors duration-200 rounded-full flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",
                        fromSubagentPanel ? "hidden" : "",
                        sendAsHimRequest
                          ? "bg-blue-500/20 border border-blue-500/50 hover:bg-blue-500/30"
                          : "bg-[#FFFFFF14] hover:bg-gray-100/10"
                      )}
                    >
                      <span
                        className={cn(
                          "text-[14px] font-medium",
                          sendAsHimRequest ? "text-blue-400" : "text-[#E6E6E6]"
                        )}
                      >
                        HIM
                      </span>
                    </button>
                  )}
                  <TooltipProvider>
                    <Tooltip
                      open={showForkTooltip || undefined}
                      delayDuration={hasSeenForkIntroLocal ? 0 : 9999999}
                    >
                      <TooltipTrigger asChild>
                        <div
                          className={cn(
                            "relative transition-all duration-200",
                            showForkTooltip
                              ? "before:absolute before:inset-[-4px] before:border before:border-white/20 before:rounded-full before:shadow-lg before:pointer-events-none"
                              : ""
                          )}
                        >
                          <button
                            type="button"
                            disabled={
                              (agentStatus !== "waiting" &&
                                agentStatus !== "subagent_waiting") ||
                              buildMode != "build" ||
                              isSubagentActive
                            }
                            onClick={() => {
                              if (forked_status === "running") return;
                              // Dismiss tooltip when button is clicked
                              if (showForkTooltip) {
                                handleDismissForkTooltip();
                              }
                              modalOpen &&
                                setModalOpen &&
                                setModalOpen({
                                  ...modalOpen,
                                  fork: !modalOpen.fork,
                                });
                            }}
                            className={cn(
                              `px-3 h-[40px] py-2 transition-colors duration-200 rounded-full flex items-center gap-[6px] bg-[#FFFFFF14] hover:bg-gray-100/10 disabled:opacity-50 disabled:cursor-not-allowed`,
                              fromSubagentPanel ? "hidden" : "",
                              forked_status === "running"
                                ? "bg-[#80FFF90D] hover:bg-[#80FFF90D] disabled:opacity-100 cursor-not-allowed"
                                : ""
                            )}
                          >
                            <img
                              src={
                                forked_status == "running"
                                  ? animatedSpinner
                                  : ForkSVG
                              }
                              alt="Fork"
                              className="w-5 h-5"
                            />
                            <span
                              className={cn(
                                "text-[14px] font-medium text-[#E6E6E6]",
                                forked_status == "running" ? "text-[#80FFF9]" : ""
                              )}
                            >
                              {forked_status === "running"
                                ? "Forking..."
                                : "Fork"}
                            </span>
                          </button>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        side="top"
                        align="center"
                        className={cn(
                          hasSeenForkIntroLocal
                            ? "bg-[#DDDDE6] font-['Inter'] text-black border-0"
                            : "relative p-0 bg-none border-none pb-[30px] -bottom-[8px] bg-transparent"
                        )}
                      >
                        {hasSeenForkIntroLocal ? (
                          // Simple hover tooltip
                          <>
                            <span className="text-sm font-['Inter'] font-medium">Fork into a new chat</span>
                            <TooltipArrow className="fill-[#DDDDE6]" />
                          </>
                        ) : (
                          // Introduction tooltip
                          <motion.div
                            initial={{ opacity: 0, y: 6 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 6 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            className="max-w-[260px] p-3 bg-[#FFFFFF] border-[#2E2F34] text-white shadow-2xl rounded-[12px]"
                            style={{ backdropFilter: "blur(20px)" }}
                          >
                            <div className="flex flex-col gap-4">
                              <div className="flex flex-col gap-1">
                                <div className="flex items-center justify-between">
                                  <span className="text-[16px] font-['Inter'] font-semibold text-[#0F0F10]">
                                    Introducing Forks
                                  </span>
                                </div>
                                <p className="text-[12px] font-medium font-['Inter'] text-[#0F0F1080] leading-[16px]">
                                  Continue in a new chat with key context preserved. Perfect for branching tasks.
                                </p>
                              </div>
                              <div className="flex items-center justify-end w-full gap-2">
                                <button
                                  onClick={()=>{
                                    window.open(
                                      URL_LINKS.forking.learnMore,
                                      "_blank"
                                    );
                                    handleDismissForkTooltip();
                                  }}
                                  className="text-[12px] px-2 py-[4px] font-['Inter'] font-medium rounded-[5px] bg-[#0B0B0B24] hover:bg-[#0B0B0B34] text-[#00000080] transition-colors"
                                  type="button"
                                >
                                  Learn More
                                </button>
                                <button
                                  onClick={handleDismissForkTooltip}
                                  className="px-2 py-[4px] bg-[#0B0B0B] hover:bg-[#333333]  font-['Inter'] rounded-[5px] text-[#FFFFFF] text-[12px] font-medium transition-colors"
                                  type="button"
                                >
                                  Okay, got it
                                </button>
                              </div>
                            </div>
                            <div className="absolute z-[999] flex flex-col items-center justify-center transform -translate-x-1/2 -bottom-[25px] left-1/2">
                              <div className="bg-white min-h-5 min-w-[1px] mb-[2px]"></div>
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          </motion.div>
                        )}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
              </div>
              <button
                onClick={handleSubmitForm}
                className={`flex ${
                  agentStatus === "subagent_running" && showFinishSubagentText
                    ? "w-auto px-3"
                    : "w-[48px] px-[12px]"
                } py-[6px] justify-center items-center gap-[10px] rounded-[38px] bg-[#DDDDE6] disabled:opacity-50`}
                type="submit"
                disabled={disabledSubmit}
              >
                {agentStatus === "subagent_running" &&
                  showFinishSubagentText && (
                    <span className="text-sm font-medium text-black">
                      Finish Sub-Agent
                    </span>
                  )}
                {buttonIcon}
              </button>
            </div>
          </form>

          {/* GitHub Push Modal */}
          {isGitHubPushModalOpen && (
            <GitHubPushModal
              isOpen={isGitHubPushModalOpen}
              onOpenChange={(open) => setIsGitHubPushModalOpen(open)}
              jobId={jobDetails?.job_id || ""}
              onSuccess={(repoDetails) => {
                // Pass the repository details to the parent component
                if (repoDetails && onGitHubPush) {
                  onGitHubPush(repoDetails);
                }
                setIsGitHubPushModalOpen(false);
              }}
              lastGithubUsed={lastGithubUsed}
            />
          )}
        </motion.div>
      </AnimatePresence>
    );
  }
);
