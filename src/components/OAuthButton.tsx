import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";

import GoogleSVG from "@/assets/Google.svg";
import GithubGreen from "@/assets/GithubGreen.svg";
import { app } from "electron";

type OAuthProviderType = "google" | "github" | "apple";

interface OAuthButtonProps {
  provider: OAuthProviderType;
  onClick: () => Promise<void>;
  isSubmitting: boolean;
  buttonText?: string;
  className?: string;
}

const OAuthButton: React.FC<OAuthButtonProps> = ({
  provider,
  onClick,
  isSubmitting,
  buttonText,
  className = ""
}) => {
  // Provider configs
  const providerConfig = {
    google: {
      icon: GoogleSVG, // Update this with your actual import path
      defaultText: "Sign in with Google",
    },
    github: {
      icon: GithubGreen, // Update this with your actual import path
      defaultText: "Sign in with Gith<PERSON>",
    },
    apple: {
      icon: GoogleSVG, // Update this with your actual import path
      defaultText: "Sign in with <PERSON>",
    },
  };

  const { icon, defaultText } = providerConfig[provider];
  const text = buttonText || defaultText;

  return (
    <Button
    type="button"
    className={`w-full h-[40px] md:h-[56px] p-0 bg-[#18181A] hover:bg-[#313133] border-[#292929] border text-[#CCCCCC] hover:text-white flex items-center justify-center ${className}`}
    onClick={onClick}
    disabled={isSubmitting}
  >
    <div className="flex items-center w-full relative font-medium tracking-[-1%]">
      {isSubmitting ? (
        <Spinner className="mx-auto" />
      ) : (
        <>
          <img src={icon} alt={`${provider} icon`} className="absolute w-6 h-6 left-4" />
          <span className="w-full text-center">{text}</span>
        </>
      )}
    </div>
  </Button>
  );
};

export default OAuthButton;