import DropdownCyan from "@/assets/fork/dropdown_cyan.svg";
// @ts-ignore
import animatedSpinner from "@/assets/animated-spinner.gif";
// @ts-ignore
import MatrixCodeGIF from "@/assets/fork/matrix_code.gif";
import InitialState from "@/assets/fork/first_state.svg";
import CompletedState from "@/assets/fork/completed.svg";
import ForkFill from "@/assets/fork/fork-fill.svg"
import { memo, useEffect, useState, useRef } from "react";
import { cn } from "@/lib/utils";

import { motion, AnimatePresence } from "framer-motion";

import RefreshSVG from "@/assets/refresh.svg"

import ForkRed from "@/assets/fork/fork_red.svg"

import { Button } from "../ui/button";

import SessionSVG from "@/assets/fork/session.svg"

import SummarySVG from "@/assets/fork/summary.svg"

import ForkedFrom from "@/assets/fork/forked_from.svg"
import { useOpenJobTab } from "@/hooks/useOpenJobTab";
import { agentApi } from "@/services/agentApi";


interface ForkBarProps {
  isDropdownOpen: boolean;
  setIsDropdownOpen: (open: boolean) => void;
  state: "running" | "completed" | "error" | "forked";
  message: any;
  count?: number;
  forked?: {
    parent_job_id: string | null;
    parent_job_title: string | null;
  };
  onViewSummary?: () => void;
  currentJobId?: string;
  messages?: any[];
  onOpenForkModal?: (jobId: string, messages: any[]) => void;
  onOpenForkModalWithSummary?: (jobId: string, summaryData: string) => void;
  podIsPaused?: boolean;
  // onPreviewClick?: () => void;
}


const ForkBar = memo(
  ({ isDropdownOpen, setIsDropdownOpen, state, message, forked, count, onViewSummary, currentJobId, messages = [], onOpenForkModal, onOpenForkModalWithSummary, podIsPaused }: ForkBarProps) => {

    const { openJobTab } = useOpenJobTab();

    // Time-based step completion state
    const [elapsedTime, setElapsedTime] = useState(0); // in seconds
    const [completedSteps, setCompletedSteps] = useState(0);
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const stepTimingsRef = useRef<number[]>([]);

    // Step state type
    type StepState = 'pending' | 'running' | 'completed';

    // Step configuration with different text for each state
    const stepConfig = [
      {
        pending: `Initializing fork process...`,
        running: `Initializing fork process...`,
        completed: `Fork process initialized`
      },
      {
        pending: "Copying project files...",
        running: "Copying project files...",
        completed: "Project files copied"
      },
      {
        pending: "Setting up workspace...",
        running: "Setting up workspace...",
        completed: "Workspace ready"
      },
      {
        pending: "Finalizing environment...",
        running: "Finalizing environment...",
        completed: "Environment finalized"
      }
    ];

    // Get step state based on current progress
    const getStepState = (stepIndex: number): StepState => {
      if (stepIndex < completedSteps) return 'completed';
      if (stepIndex === completedSteps) return 'running';
      return 'pending';
    };

    // Get step text based on state
    const getStepText = (stepIndex: number): string => {
      const state = getStepState(stepIndex);
      return stepConfig[stepIndex][state];
    };

    // Generate random step timings (40-70 seconds each)
    const generateStepTimings = () => {
      const timings = [];
      let cumulativeTime = 0;

      for (let i = 0; i < stepConfig.length; i++) {
        // Random time between 40-70 seconds for each step
        const stepDuration = Math.floor(Math.random() * 10) + 10; // 10 - 20 sec
        cumulativeTime += stepDuration;
        timings.push(cumulativeTime);
      }

      return timings;
    };

    // Time-based step completion effect
    useEffect(() => {
      const isRunningWithoutForkedId = state === "running" && (!message || !message.forked_job_id);

      if (isRunningWithoutForkedId && message?.timestamp) {
        // Generate random step timings if not already generated
        if (stepTimingsRef.current.length === 0) {
          stepTimingsRef.current = generateStepTimings();
        }

        // Clear any existing timer
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }

        // Set up timer to update every second
        timerRef.current = setInterval(() => {
          const now = new Date();
          const messageTime = new Date(message.timestamp);
          const elapsed = Math.floor((now.getTime() - messageTime.getTime()) / 1000);
          setElapsedTime(elapsed);

          // Calculate completed steps based on random timings
          let newCompletedSteps = 0;
          for (let i = 0; i < stepTimingsRef.current.length; i++) {
            if (elapsed >= stepTimingsRef.current[i]) {
              newCompletedSteps = i + 1;
            } else {
              break;
            }
          }

          // Keep the last step always in progress (never complete all steps)
          setCompletedSteps(Math.min(newCompletedSteps, stepConfig.length - 1));
        }, 1000);
      } else {
        // Clean up timer when not running or when forked_job_id is available
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }

        // Reset state when not in running mode
        if (state !== "running") {
          setElapsedTime(0);
          setCompletedSteps(0);
          stepTimingsRef.current = []; // Reset step timings
        }
      }

      // Cleanup on unmount
      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }, [state, message?.forked_job_id, message?.timestamp, stepConfig.length]);

    // Format elapsed time for display
    const formatElapsedTime = (seconds: number): string => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      if (mins > 0) {
        return `${mins}m ${secs}s`;
      }
      return `${secs}s`;
    };

    const handleSessionClick = async () => {
      if(message && message.forked_job_id){
        await openJobTab(message.forked_job_id);
      }
    }

    const handlePreviousSessionClick = async () => {
      if(forked && forked.parent_job_id){
        await openJobTab(forked.parent_job_id);
      }
    }

    const handleViewSummaryClick = async () => {
      if (!onOpenForkModalWithSummary) {
        // Fallback to the original onViewSummary if provided
        onViewSummary?.();
        return;
      }

      let targetJobId: string | undefined;

      if (state === "forked" && currentJobId) {
        // If we're in forked state, use the current job ID
        targetJobId = currentJobId;
      } else if (message?.forked_job_id) {
        // If the message has forked details, use that job ID
        targetJobId = message.forked_job_id;
      } else if (currentJobId) {
        // Fallback to current job ID
        targetJobId = currentJobId;
      }

      if (targetJobId) {
        try {
          console.log("Fetching summary for job:", targetJobId);
          const summaryResponse = await agentApi.viewSummary(targetJobId);
          console.log("Summary API Response:", summaryResponse);

          // Open the ForkModal with the retrieved summary data
          onOpenForkModalWithSummary(targetJobId, summaryResponse.summary || "");
        } catch (error) {
          console.error("Error fetching summary:", error);
          // Fallback to opening modal without summary data
          onOpenForkModal?.(targetJobId, messages);
        }
      } else {
        console.error("No job ID available for viewing summary");
      }
    };

    
    if (state == "running") {
      return (
        <div className="relative flex items-center justify-center w-full mb-[2rem]">
          <motion.div
            className="mt-[1rem] relative flex justify-center px-4 items-center w-full overflow-hidden bg-[#80fff908]"
            animate={{
              minHeight: isDropdownOpen ? "180px" : "72px"
            }}
            transition={{
              duration: 0.4,
              ease: [0.25, 0.1, 0.25, 1]
            }}
          >
            <div
              className="absolute flex overflow-hidden top-0 bottom-0 left-0 right-0 w-full opacity-20 h-[280px]"
            >
              <img
                src={MatrixCodeGIF}
                alt="Matrix Code"
                className="object-cover h-[280px]"
              />
              <img
                src={MatrixCodeGIF}
                alt="Matrix Code"
                className="object-cover h-[280px] rotate-180"
              />
              <img
                src={MatrixCodeGIF}
                alt="Matrix Code"
                className="object-cover h-[280px] "
              />
              <img
                src={MatrixCodeGIF}
                alt="Matrix Code"
                className="object-cover h-[280px] rotate-180"
              />
              <img
                src={MatrixCodeGIF}
                alt="Matrix Code"
                className="object-cover h-[280px] "
              />
              <img
                src={MatrixCodeGIF}
                alt="Matrix Code"
                className="object-cover h-[280px] rotate-180"
              />
              <img
                src={MatrixCodeGIF}
                alt="Matrix Code"
                className="object-cover h-[280px]"
              />
              <img
                src={MatrixCodeGIF}
                alt="Matrix Code"
                className="object-cover h-[280px] rotate-180"
              />
            </div>
            <motion.div
              className="w-full max-w-4xl z-[10] flex flex-col gap-[10px] justify-center py-[20px]"
              animate={{
                minHeight: isDropdownOpen ? "180px" : "72px"
              }}
              transition={{
                duration: 0.4,
                ease: [0.25, 0.1, 0.25, 1]
              }}
            >
              {/* Main row */}
              <div className="flex items-center justify-between w-full cursor-pointer" onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
                <div className="flex justify-start items-center max-md:flex-col max-md:items-center gap-1 md:gap-2 z-[10]">
                  <button
                    type="button"
                    className="flex items-center gap-2 transition-opacity hover:opacity-80"
                  >
                    <motion.img
                      src={DropdownCyan}
                      alt="Dropdown"
                      className="w-6 h-6"
                      animate={{
                        rotate: isDropdownOpen ? 90 : 0
                      }}
                      transition={{
                        duration: 0.3,
                        ease: [0.25, 0.1, 0.25, 1]
                      }}
                    />
                    <span className="text-[#80FFF9] text-left max-md:text-[14px] font-medium tracking-[0.2px] drop-shadow-[0px_0px_40px_rgba(128,255,249,0.4)]">
                      Forking a new session
                    </span>
                  </button>
                  <motion.span
                    className="text-[#80FFF980] max-md:text-[13px] text-[14px] font-jetbrains font-medium"
                    animate={{
                      opacity: isDropdownOpen ? 0 : 1,
                      x: isDropdownOpen ? -10 : 0
                    }}
                    transition={{
                      duration: 0.3,
                      ease: [0.25, 0.1, 0.25, 1]
                    }}
                  >
                    {" "}
                    {completedSteps < stepConfig.length ? getStepText(completedSteps) : "Finalizing..."}
                  </motion.span>
                </div>
                <div className="flex items-center gap-4 z-[10]">
                  <span className="text-[#80FFF980] font-jetbrains font-medium text-[14px]">{formatElapsedTime(elapsedTime)}</span>
                  <div className="flex items-center gap-2 bg-[#80FFF91A] rounded-[10px] h-8 w-8 justify-center">
                    <img
                      src={animatedSpinner}
                      alt="Loading"
                      className="w-6 h-6"
                    />
                  </div>
                </div>
              </div>

              {/* Expanded Content */}
              <AnimatePresence>
                {isDropdownOpen && (
                  <motion.div
                    className="flex flex-col gap-[10px] z-[10]"
                    initial={{
                      opacity: 0,
                      height: 0,
                      y: -10
                    }}
                    animate={{
                      opacity: 1,
                      height: "auto",
                      y: 0
                    }}
                    exit={{
                      opacity: 0,
                      height: 0,
                      y: -10
                    }}
                    transition={{
                      duration: 0.4,
                      ease: [0.25, 0.1, 0.25, 1]
                    }}
                  >
                    {stepConfig.map((_, index) => {
                      const stepState = getStepState(index);
                      const stepText = getStepText(index);

                      return (
                        <motion.div
                          key={index}
                          className={cn(
                            "flex items-center gap-2 transition-colors rounded-md",
                            stepState === 'pending' ? "opacity-50" :
                            stepState === 'completed' ? "opacity-75" : ""
                          )}
                          initial={{
                            opacity: 0,
                            x: -20
                          }}
                          animate={{
                            opacity: stepState === 'pending' ? .5 : stepState === 'completed' ? .75 : 1,
                            x: 0
                          }}
                          transition={{
                            duration: 0.3,
                            delay: index * 0.1,
                            ease: [0.25, 0.1, 0.25, 1]
                          }}
                        >
                          <img
                            src={
                              stepState === 'completed'
                                ? CompletedState
                                : stepState === 'running'
                                  ? animatedSpinner
                                  : InitialState
                            }
                            alt="Step"
                            className="w-6 h-6"
                          />
                          <span className={cn(
                            "text-[14px] font-jetbrains font-medium drop-shadow-[0px_0px_40px_rgba(128,255,249,0.4)]",
                            stepState === 'completed' ? "text-[#80FFF9]" :
                            stepState === 'running' ? "text-[#80FFF9]" :
                            "text-[#80FFF9]"
                          )}>
                            {stepText}
                          </span>
                        </motion.div>
                      );
                    })}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </motion.div>
        </div>
      );
    }

    if (state == "completed") {
      return (
        <div className="relative flex items-center justify-center w-full">
          <div
            className={`mt-[1rem] bg-gradient-to-r px-4 from-[#80FFF901] via-[#80FFF915] to-[#80FFF901] relative flex justify-center items-center w-full transition-all duration-300 overflow-hidden`}
          >
            <div
              className={`w-full max-w-4xl z-[10] flex flex-col transition-all duration-300  min-h-[72px]`}
            >
              {/* Main row */}
              <div className="flex items-center justify-between w-full h-[72px]">
                <div className="flex items-center gap-2 z-[10]">
                  <button
                    type="button"
                    className="flex items-center gap-2 transition-opacity pointer-events-none hover:opacity-80"
                  >
                    <img
                      src={ForkFill}
                      alt="Dropdown"
                      className={`w-6 h-6 transition-transform duration-200}`}
                    />
                    <span className="text-[#80FFF9] font-medium tracking-[0.2px] drop-shadow-[0px_0px_40px_rgba(128,255,249,0.4)]">
                      Session forked successfully
                    </span>
                  </button>
                </div>
                <div className="flex items-center gap-2 z-[10]">

                  <Button
                  variant="default"
                  className="group h-[32px] py-2 px-[10px] bg-[#FFFFFF1A] rounded-[8px] hover:bg-white/20 transition-colors duration-200"
                  // onClick={handlePreviewClick}
                  // disabled={disabledPodRelated}
                  // onClick={handlePreviousSessionClick}
                  onClick={handleViewSummaryClick}
                >
                  <div className="flex items-center gap-2">
                    <img alt="preview" src={SummarySVG} className="w-5 h-5" />
                    <span className="hidden text-[15px] font-medium font-['Inter'] text-[#FFFFFF] md:block">
                      View Summary
                    </span>
                  </div>
                </Button>

                <Button
                  variant="default"
                  className="group h-[32px] py-2 px-[10px] bg-[#80FFF91A] rounded-[8px] hover:bg-[#80FFF9]/20 transition-colors duration-200"
                  // onClick={handlePreviewClick}
                  // disabled={disabledPodRelated}
                >
                  <div className="flex items-center gap-2" onClick={handleSessionClick}>
                    <img alt="preview" src={SessionSVG} className="w-4 h-4" />
                    <span className="hidden text-[15px] font-medium font-['Inter'] text-[#80FFF9] md:block">
                      View Session
                    </span>
                  </div>
                </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (state == "error") {
      return (
        <div className="relative flex items-center justify-center w-full">
          <div
            className={`mt-[1rem] bg-gradient-to-r px-4 from-[#FF808001] via-[#FF808015] to-[#FF808001] relative flex justify-center items-center w-full transition-all duration-300 overflow-hidden`}
          >
            <div
              className={`w-full max-w-4xl z-[10] flex flex-col transition-all duration-300  min-h-[72px]`}
            >
              {/* Main row */}
              <div className="flex items-center justify-between w-full h-[72px]">
                <div className="flex items-center gap-2 z-[10]">
                  <button
                    type="button"
                    className="flex items-center gap-2 transition-opacity pointer-events-none hover:opacity-80"
                  >
                    <img
                      src={ForkRed}
                      alt="Dropdown"
                      className={`w-6 h-6 scale-[2] transition-transform duration-200}`}
                    />
                    <span className="text-[#FF8080] font-medium tracking-[0.2px] drop-shadow-[0px_0px_40px_rgba(128,255,249,0.4)]">
                      Failed to fork session
                    </span>
                  </button>
                </div>
                <div className="flex items-center gap-2 z-[10]">

                <Button
                  variant="default"
                  disabled={podIsPaused}
                  className="group h-[32px] py-2 px-[10px] bg-[#FFFFFF1A] rounded-[8px] hover:bg-[#FFFFFF]/20 transition-colors duration-200 disabled:cursor-not-allowed disabled:opacity-50"
                  onClick={()=>{
                    currentJobId && messages &&
                    onOpenForkModal?.(currentJobId, messages);
                  }}
                >
                  <div className="flex items-center gap-2" >
                    <img alt="preview" src={RefreshSVG} className="w-4 h-4" />
                    <span className="hidden text-[15px] font-medium font-['Inter'] text-[#fff] md:block">
                      Retry Fork
                    </span>
                  </div>
                </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (state == "forked") {
      return (
        <div className="relative flex items-center justify-center w-full mt-4">
          <motion.div
            className="bg-gradient-to-r px-4 from-[#80FFF901] via-[#80FFF915] to-[#80FFF901] relative flex justify-center items-center w-full overflow-hidden"
            animate={{
              minHeight: isDropdownOpen ? "180px" : "72px"
            }}
            transition={{
              duration: 0.4,
              ease: [0.25, 0.1, 0.25, 1]
            }}
          >
            <motion.div
              className="w-full max-w-4xl z-[10] flex flex-col min-h-[72px]"
              animate={{
                minHeight: isDropdownOpen ? "180px" : "72px"
              }}
              transition={{
                duration: 0.4,
                ease: [0.25, 0.1, 0.25, 1]
              }}
            >
              {/* Main row */}
              <div className="flex items-center justify-between w-full h-[72px]">
                <div className="flex items-center gap-2 z-[10]">
                  <button
                    type="button"
                    className="flex items-center gap-3 transition-opacity pointer-events-none hover:opacity-80"
                  >
                    <div className="md:w-[40px] md:h-[40px] flex items-center justify-center">
                      <img
                      src={ForkFill}
                      alt="Dropdown"
                      className="w-5 h-5 md:w-8 md:h-8"
                    />
                    </div>
                    <span className="text-[#80FFF9] max-md:text-[14px]  text-nowrap font-medium tracking-[0.2px] drop-shadow-[0px_0px_40px_rgba(128,255,249,0.4)]">
                      Forked from
                    </span>
                  </button>

                  <button
                    type="button"
                    // onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="flex items-center gap-2 transition-opacity pointer-events-none hover:opacity-80"
                  >
                    <motion.img
                      src={ForkedFrom}
                      alt="Dropdown"
                      className="w-4 h-4 md:w-6 md:h-6"
                      animate={{
                        rotate: isDropdownOpen ? 90 : 0
                      }}
                      transition={{
                        duration: 0.3,
                        ease: [0.25, 0.1, 0.25, 1]
                      }}
                    />
                    <span className="text-[#fff] max-md:text-[14px] font-medium capitalize tracking-[0.2px] drop-shadow-[0px_0px_40px_rgba(128,255,249,0.4)]">
                      {forked?.parent_job_title?.replaceAll("-", " ")}
                    </span>
                  </button>
                </div>
                <div className="flex items-center gap-2 z-[10]">
                  <Button
                  variant="default"
                  className="group h-[32px] py-2 px-[10px] bg-[#FFFFFF1A] rounded-[8px] hover:bg-white/20 transition-colors duration-200"
                  // onClick={handlePreviewClick}
                  // disabled={disabledPodRelated}
                  // onClick={handlePreviousSessionClick}
                  onClick={handleViewSummaryClick}
                >
                  <div className="flex items-center gap-2">
                    <img alt="preview" src={SummarySVG} className="w-5 h-5" />
                    <span className="hidden text-[15px] font-medium font-['Inter'] text-[#FFFFFF] md:block">
                      View Summary
                    </span>
                  </div>
                </Button>

                <Button
                  id="previous-session-button"
                  variant="default"
                  className="group h-[32px] py-2 px-[10px] bg-[#80FFF91A] rounded-[8px] hover:bg-[#80FFF9]/20 transition-colors duration-200"
                  // onClick={handlePreviewClick}
                  // disabled={disabledPodRelated}
                  onClick={handlePreviousSessionClick}
                >
                  <div className="flex items-center gap-2">
                    <img alt="preview" src={SessionSVG} className="w-4 h-4" />
                    <span className="hidden text-[15px] font-medium font-['Inter'] text-[#80FFF9] md:block">
                      Previous Session
                    </span>
                  </div>
                </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      );
    }
  }
);

ForkBar.displayName = "ForkBar";

export default ForkBar;
